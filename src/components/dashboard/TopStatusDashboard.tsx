import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from '../common';
import { COLORS } from '../../utils/constants';
import { OverallStats, PredictedPassRate } from '../../types/statistics';

interface TopStatusDashboardProps {
  stats: OverallStats;
  prediction: PredictedPassRate;
  userName?: string;
}

export function TopStatusDashboard({ stats, prediction, userName = '用戶' }: TopStatusDashboardProps) {
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 6) return '深夜好';
    if (hour < 12) return '早上好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  const getColorByPercentage = (percentage: number) => {
    if (percentage >= 80) return COLORS.SUCCESS;
    if (percentage >= 60) return COLORS.WARNING;
    return COLORS.ERROR;
  };

  const masteredQuestions = stats.volumeStats.reduce((total, volume) => total + volume.correctAnswers, 0);
  const totalQuestions = stats.volumeStats.reduce((total, volume) => total + volume.totalQuestions, 0);

  return (
    <Card style={styles.container}>
      {/* 問候語 */}
      <View style={styles.greetingSection}>
        <Text style={styles.greetingText}>
          {getGreeting()}，{userName}！
        </Text>
      </View>

      {/* 核心指標：預測合格率 */}
      <View style={styles.coreMetricSection}>
        <View style={styles.circularProgress}>
          <Text style={[styles.percentageText, { color: getColorByPercentage(prediction.percentage) }]}>
            {prediction.percentage}%
          </Text>
          <Text style={styles.metricLabel}>預測合格率</Text>
        </View>
      </View>

      {/* 輔助數據 */}
      <View style={styles.auxiliaryDataSection}>
        <View style={styles.dataRow}>
          <View style={styles.dataItem}>
            <Text style={styles.dataValue}>{masteredQuestions} / {totalQuestions}</Text>
            <Text style={styles.dataLabel}>已掌握題目</Text>
          </View>
          <View style={styles.dataItem}>
            <Text style={styles.dataValue}>{stats.accuracy.toFixed(1)}%</Text>
            <Text style={styles.dataLabel}>總正確率</Text>
          </View>
          <View style={styles.dataItem}>
            <Text style={styles.dataValue}>5</Text>
            <Text style={styles.dataLabel}>已獲徽章</Text>
          </View>
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    paddingVertical: 24,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  greetingSection: {
    marginBottom: 20,
  },
  greetingText: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.TEXT,
    textAlign: 'center',
  },
  coreMetricSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  circularProgress: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: 8,
    borderColor: COLORS.BORDER,
    backgroundColor: COLORS.BACKGROUND,
  },
  percentageText: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
    fontWeight: '500',
  },
  auxiliaryDataSection: {
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingTop: 16,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  dataItem: {
    alignItems: 'center',
    flex: 1,
  },
  dataValue: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
    marginBottom: 4,
  },
  dataLabel: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
    textAlign: 'center',
  },
});
