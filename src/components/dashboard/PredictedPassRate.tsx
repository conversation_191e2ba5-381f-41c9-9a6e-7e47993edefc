import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from '../common';
import { COLORS } from '../../utils/constants';
import { PredictedPassRate } from '../../types/statistics';

interface PredictedPassRateProps {
  prediction: PredictedPassRate;
}

export function PredictedPassRateComponent({ prediction }: PredictedPassRateProps) {
  const getColorByPercentage = (percentage: number) => {
    if (percentage >= 80) return COLORS.SUCCESS;
    if (percentage >= 60) return COLORS.WARNING;
    return COLORS.ERROR;
  };

  const getConfidenceColor = (confidence: PredictedPassRate['confidence']) => {
    switch (confidence) {
      case 'high': return COLORS.SUCCESS;
      case 'medium': return COLORS.WARNING;
      case 'low': return COLORS.ERROR;
      default: return COLORS.SECONDARY_TEXT;
    }
  };

  const getConfidenceText = (confidence: PredictedPassRate['confidence']) => {
    switch (confidence) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return '';
    }
  };

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>考試通過率預測</Text>
        <View style={styles.confidenceBadge}>
          <Text style={[styles.confidenceText, { color: getConfidenceColor(prediction.confidence) }]}>
            可信度：{getConfidenceText(prediction.confidence)}
          </Text>
        </View>
      </View>

      <View style={styles.predictionSection}>
        <Text style={[styles.percentage, { color: getColorByPercentage(prediction.percentage) }]}>
          {prediction.percentage.toFixed(0)}%
        </Text>
        <Text style={styles.predictionLabel}>預測通過率</Text>
      </View>

      {prediction.recommendations.length > 0 && (
        <View style={styles.recommendationsSection}>
          <Text style={styles.recommendationsTitle}>建議</Text>
          {prediction.recommendations.map((recommendation, index) => (
            <Text key={index} style={styles.recommendationItem}>
              • {recommendation}
            </Text>
          ))}
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 12,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '500',
  },
  predictionSection: {
    alignItems: 'center',
    marginBottom: 16,
  },
  percentage: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  predictionLabel: {
    fontSize: 16,
    color: COLORS.SECONDARY_TEXT,
    marginTop: 4,
  },
  recommendationsSection: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  recommendationItem: {
    fontSize: 14,
    color: COLORS.TEXT,
    lineHeight: 20,
    marginBottom: 4,
  },
});