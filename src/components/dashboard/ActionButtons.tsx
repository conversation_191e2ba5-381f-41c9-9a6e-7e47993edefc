import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Button } from '../common';

interface ActionButtonsProps {
  onStartPractice: () => void;
  onStartExam: () => void;
}

export function ActionButtons({
  onStartPractice,
  onStartExam,
}: ActionButtonsProps) {
  return (
    <View style={styles.container}>
      {/* 主要按鈕：開始模擬考試 */}
      <View style={styles.primaryButtonContainer}>
        <View style={styles.buttonIconContainer}>
          <Text style={styles.buttonIcon}>⏱️</Text>
        </View>
        <View style={styles.buttonContent}>
          <Text style={styles.buttonTitle}>開始模擬考試</Text>
          <Text style={styles.buttonDescription}>完全模擬真實考試，檢驗你的學習成果</Text>
        </View>
        <Button
          title="開始"
          onPress={onStartExam}
          style={styles.primaryActionButton}
          textStyle={styles.primaryActionButtonText}
        />
      </View>

      {/* 次要按鈕：進入練習模式 */}
      <View style={styles.secondaryButtonContainer}>
        <View style={styles.buttonIconContainer}>
          <Text style={styles.buttonIcon}>📚</Text>
        </View>
        <View style={styles.buttonContent}>
          <Text style={styles.buttonTitle}>進入練習模式</Text>
          <Text style={styles.buttonDescription}>按章節、按標籤自由練習</Text>
        </View>
        <Button
          title="練習"
          onPress={onStartPractice}
          variant="outline"
          style={styles.secondaryActionButton}
          textStyle={styles.secondaryActionButtonText}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    gap: 16,
  },
  primaryButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.SUCCESS,
  },
  secondaryButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY,
  },
  buttonIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.BACKGROUND,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  buttonIcon: {
    fontSize: 24,
  },
  buttonContent: {
    flex: 1,
    marginRight: 16,
  },
  buttonTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
    marginBottom: 4,
  },
  buttonDescription: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
    lineHeight: 20,
  },
  primaryActionButton: {
    backgroundColor: COLORS.SUCCESS,
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
    minWidth: 80,
  },
  primaryActionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryActionButton: {
    borderColor: COLORS.PRIMARY,
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
    minWidth: 80,
  },
  secondaryActionButtonText: {
    color: COLORS.PRIMARY,
    fontSize: 16,
    fontWeight: '600',
  },
});