import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button } from '../common';
import { COLORS } from '../../utils/constants';

interface ActionButtonsProps {
  onStartPractice: () => void;
  onStartExam: () => void;
  onViewHistory: () => void;
  onViewWrongQuestions: () => void;
}

export function ActionButtons({
  onStartPractice,
  onStartExam,
  onViewHistory,
  onViewWrongQuestions,
}: ActionButtonsProps) {
  return (
    <View style={styles.container}>
      <View style={styles.primaryActions}>
        <Button
          title="開始練習"
          onPress={onStartPractice}
          style={[styles.primaryButton, { backgroundColor: COLORS.PRIMARY }] as any}
        />
        <Button
          title="模擬考試"
          onPress={onStartExam}
          variant="secondary"
          style={[styles.primaryButton, { backgroundColor: COLORS.SUCCESS }] as any}
          textStyle={{ color: '#ffffff' }}
        />
      </View>

      <View style={styles.secondaryActions}>
        <Button
          title="錯題集"
          onPress={onViewWrongQuestions}
          variant="outline"
          style={styles.secondaryButton}
        />
        <Button
          title="練習記錄"
          onPress={onViewHistory}
          variant="outline"
          style={styles.secondaryButton}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  primaryActions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  primaryButton: {
    flex: 1,
    minHeight: 52,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
  },
});