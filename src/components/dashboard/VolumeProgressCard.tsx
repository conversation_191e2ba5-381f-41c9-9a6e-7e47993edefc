import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { VolumeStats } from '../../types/statistics';
import { COLORS, VOLUMES } from '../../utils/constants';
import { Card } from '../common';
import { ProgressBar } from '../question';

interface VolumeProgressCardProps {
  volumeStats: VolumeStats;
}

export function VolumeProgressCard({ volumeStats }: VolumeProgressCardProps) {
  const { volume, totalQuestions, seenQuestions, correctAnswers, accuracy } = volumeStats;
  const progressPercentage = totalQuestions > 0 ? (seenQuestions / totalQuestions) * 100 : 0;

  const getProgressColor = (accuracy: number) => {
    if (accuracy >= 80) return COLORS.SUCCESS;
    if (accuracy >= 60) return COLORS.WARNING;
    return COLORS.ERROR;
  };

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.volumeNumber}>第 {volume} 冊</Text>
        <Text style={styles.accuracyText}>
          正確率 {accuracy.toFixed(0)}%
        </Text>
      </View>
      
      <Text style={styles.volumeName}>
        {VOLUMES.NAMES[volume as keyof typeof VOLUMES.NAMES]}
      </Text>
      
      <View style={styles.progressSection}>
        <Text style={styles.progressText}>
          已練習 {seenQuestions} / {totalQuestions} 題
        </Text>
        <ProgressBar
          current={seenQuestions}
          total={totalQuestions}
          showNumbers={false}
          color={getProgressColor(accuracy)}
        />
      </View>
      
      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{correctAnswers}</Text>
          <Text style={styles.statLabel}>答對</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{seenQuestions - correctAnswers}</Text>
          <Text style={styles.statLabel}>答錯</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{Math.round(progressPercentage)}%</Text>
          <Text style={styles.statLabel}>完成度</Text>
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    // marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  volumeNumber: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.PRIMARY,
  },
  accuracyText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.SUCCESS,
  },
  volumeName: {
    fontSize: 16,
    color: COLORS.TEXT,
    marginBottom: 16,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressText: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
    marginBottom: 8,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
    marginTop: 4,
  },
});