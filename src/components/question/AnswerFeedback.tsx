import React from 'react';
import {
    StyleSheet,
    Text,
    View,
} from 'react-native';
import { Card } from '../common';

interface AnswerFeedbackProps {
  isCorrect: boolean;
  explanation?: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  selectedAnswer: 'A' | 'B' | 'C' | 'D';
}

export function AnswerFeedback({
  isCorrect,
  explanation,
  correctAnswer,
  selectedAnswer,
}: AnswerFeedbackProps) {
  return (
    <Card 
      style={[
        styles.container,
        isCorrect ? styles.correctContainer : styles.wrongContainer,
      ] as any}
    >
      <View style={styles.header}>
        <Text style={styles.resultEmoji}>
          {isCorrect ? '✅' : '❌'}
        </Text>
        <Text style={[
          styles.resultText,
          isCorrect ? styles.correctText : styles.wrongText,
        ]}>
          {isCorrect ? '答對了！' : '答錯了'}
        </Text>
      </View>

      <View style={styles.answerInfo}>
        <Text style={styles.answerLabel}>
          你選擇了：
          <Text style={[
            styles.answerValue,
            isCorrect ? styles.correctAnswer : styles.wrongAnswer,
          ]}>
            {selectedAnswer}
          </Text>
        </Text>
        
        {!isCorrect && (
          <Text style={styles.answerLabel}>
            正確答案：
            <Text style={[styles.answerValue, styles.correctAnswer]}>
              {correctAnswer}
            </Text>
          </Text>
        )}
      </View>

      {explanation && (
        <View style={styles.explanationContainer}>
          <Text style={styles.explanationTitle}>解釋</Text>
          <Text style={styles.explanationText}>{explanation}</Text>
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    marginBottom: 12,
  },
  correctContainer: {
    borderColor: '#34C759',
    backgroundColor: '#F0FFF4',
  },
  wrongContainer: {
    borderColor: '#FF3B30',
    backgroundColor: '#FFF5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultEmoji: {
    fontSize: 24,
    marginRight: 8,
  },
  resultText: {
    fontSize: 18,
    fontWeight: '600',
  },
  correctText: {
    color: '#34C759',
  },
  wrongText: {
    color: '#FF3B30',
  },
  answerInfo: {
    marginBottom: 12,
  },
  answerLabel: {
    fontSize: 16,
    color: '#1c1c1e',
    marginBottom: 4,
  },
  answerValue: {
    fontWeight: '600',
    fontSize: 18,
  },
  correctAnswer: {
    color: '#34C759',
  },
  wrongAnswer: {
    color: '#FF3B30',
  },
  explanationContainer: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1c1c1e',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 15,
    lineHeight: 20,
    color: '#1c1c1e',
  },
});