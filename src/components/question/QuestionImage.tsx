import React from 'react';
import {
  View,
  Image,
  StyleSheet,
  ViewStyle,
} from 'react-native';

interface QuestionImageProps {
  source: string;
  style?: ViewStyle;
}

export function QuestionImage({ source, style }: QuestionImageProps) {
  return (
    <View style={[styles.container, style]}>
      <Image
        source={{ uri: source }}
        style={styles.image}
        resizeMode="contain"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 200,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
});