import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { ProcessedQuestion } from '../../types/question';

interface OptionsListProps {
  options: ProcessedQuestion['options'];
  selectedOption?: number;
  onOptionSelect: (optionIndex: number) => void;
  showAnswer?: boolean;
  disabled?: boolean;
}

export function OptionsList({
  options,
  selectedOption,
  onOptionSelect,
  showAnswer = false,
  disabled = false,
}: OptionsListProps) {
  const getOptionStyle = (index: number) => {
    const isSelected = selectedOption === index;
    const isCorrect = showAnswer && options[index]?.isCorrect;
    const isWrong = showAnswer && selectedOption === index && !options[index]?.isCorrect;

    return [
      styles.option,
      isSelected && styles.selectedOption,
      isCorrect && styles.correctOption,
      isWrong && styles.wrongOption,
      disabled && styles.disabledOption,
    ];
  };

  const getOptionTextStyle = (index: number) => {
    const isSelected = selectedOption === index;
    const isCorrect = showAnswer && options[index]?.isCorrect;
    const isWrong = showAnswer && selectedOption === index && !options[index]?.isCorrect;

    return [
      styles.optionText,
      isSelected && styles.selectedOptionText,
      isCorrect && styles.correctOptionText,
      isWrong && styles.wrongOptionText,
    ];
  };

  const renderOption = (option: { text: string; isCorrect: boolean }, index: number) => {
    const optionLabels = ['A', 'B', 'C', 'D'];
    
    return (
      <TouchableOpacity
        key={index}
        style={getOptionStyle(index) as any}
        onPress={() => !disabled && onOptionSelect(index)}
        disabled={disabled}
        activeOpacity={0.8}
      >
        <View style={styles.optionContent}>
          <View style={styles.optionLabel}>
            <Text style={styles.optionLabelText}>{optionLabels[index]}</Text>
          </View>
          <Text style={getOptionTextStyle(index) as any}>
            {option.text}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {options.map(renderOption)}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 12,
  },
  option: {
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E5EA',
    backgroundColor: '#ffffff',
    padding: 16,
  },
  selectedOption: {
    borderColor: '#007AFF',
    backgroundColor: '#F0F8FF',
  },
  correctOption: {
    borderColor: '#34C759',
    backgroundColor: '#F0FFF4',
  },
  wrongOption: {
    borderColor: '#FF3B30',
    backgroundColor: '#FFF5F5',
  },
  disabledOption: {
    opacity: 0.6,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  optionLabel: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  optionLabelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1c1c1e',
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    color: '#1c1c1e',
    paddingTop: 6,
  },
  selectedOptionText: {
    color: '#007AFF',
    fontWeight: '500',
  },
  correctOptionText: {
    color: '#34C759',
    fontWeight: '500',
  },
  wrongOptionText: {
    color: '#FF3B30',
    fontWeight: '500',
  },
});