export const VOLUMES = {
  TOTAL: 5, // Now 5 volumes available
  NAMES: {
    1: '道路交通標誌',
    2: '道路交通標線',
    3: '道路交通燈號',
    4: '交通指揮手勢',
    5: '駕駛行為與安全',
  },
  COUNTS: {
    1: 176,
    2: 66,
    3: 150,
    4: 139,
    5: 116,
  },
} as const;

export const PRACTICE_CONFIG = {
  DEFAULT_QUESTION_COUNT: 20,
  MAX_QUESTION_COUNT: 100,
  MIN_QUESTION_COUNT: 5,
  MODES: {
    SEQUENTIAL: 'sequential',
    RANDOM: 'random',
  },
} as const;

export const EXAM_RULES = {
  TOTAL_QUESTIONS: 40,
  TIME_LIMIT_MINUTES: 50,
  PASSING_SCORE: 32, // 80%
  QUESTIONS_PER_VOLUME: {
    1: 16, // 40%
    2: 8,  // 20%
    3: 8,  // 20%
    4: 4,  // 10%
    5: 4,  // 10%
  },
} as const;

export const COLORS = {
  PRIMARY: '#007AFF',
  SUCCESS: '#34C759',
  ERROR: '#FF3B30',
  WARNING: '#FF9500',
  BACKGROUND: '#F2F2F7',
  TEXT: '#1C1C1E',
  SECONDARY_TEXT: '#8E8E93',
  BORDER: '#E5E5EA',
} as const;

export const FONTS = {
  SIZES: {
    SMALL: {
      title: 20,
      body: 14,
      caption: 12,
    },
    MEDIUM: {
      title: 24,
      body: 16,
      caption: 14,
    },
    LARGE: {
      title: 28,
      body: 18,
      caption: 16,
    },
  },
} as const;

export const TIMING = {
  AUTO_NEXT_DELAY: 2000, // 2 seconds
  ANSWER_FEEDBACK_DELAY: 1000, // 1 second
  LOADING_TIMEOUT: 10000, // 10 seconds
} as const;