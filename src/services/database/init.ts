import * as SQLite from 'expo-sqlite';
import { CREATE_TABLES, CREATE_INDEXES } from './schema';

let database: SQLite.SQLiteDatabase | null = null;

export async function initializeDatabase(): Promise<SQLite.SQLiteDatabase> {
  if (database) {
    return database;
  }

  try {
    database = await SQLite.openDatabaseAsync('macau_drive_exam.db');
    
    await database.execAsync('PRAGMA journal_mode = WAL;');
    await database.execAsync('PRAGMA foreign_keys = ON;');
    
    await database.execAsync(CREATE_TABLES);
    await database.execAsync(CREATE_INDEXES);
    
    await initializeVolumeProgress(database);
    
    console.log('Database initialized successfully');
    return database;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

export function getDatabase(): SQLite.SQLiteDatabase {
  if (!database) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return database;
}

async function initializeVolumeProgress(db: SQLite.SQLiteDatabase) {
  for (let volume = 1; volume <= 5; volume++) {
    await db.runAsync(
      `INSERT OR IGNORE INTO volume_progress (volume, total_questions) VALUES (?, ?)`,
      [volume, 0]
    );
  }
}