import { QuestionStats, VolumeProgress } from '../../types/database';
import { getDatabase } from './init';

export async function getVolumeProgress(): Promise<VolumeProgress[]> {
  const db = getDatabase();

  try {
    const result = await db.getAllAsync(
      `SELECT * FROM volume_progress ORDER BY volume ASC`
    ) as VolumeProgress[];

    return result;
  } catch (error) {
    console.error('Failed to get volume progress:', error);
    throw error;
  }
}

export async function getSeenQuestionIds(volumes: number[]): Promise<Set<number>> {
  const db = getDatabase();

  try {
    const placeholders = volumes.map(() => '?').join(',');
    const result = await db.getAllAsync(
      `SELECT DISTINCT question_id FROM question_stats
       WHERE volume IN (${placeholders}) AND total_attempts > 0`,
      volumes
    ) as { question_id: number }[];

    return new Set(result.map(row => row.question_id));
  } catch (error) {
    console.error('Failed to get seen question IDs:', error);
    throw error;
  }
}

export async function getQuestionStats(questionId: number): Promise<QuestionStats | null> {
  const db = getDatabase();
  
  try {
    const result = await db.getFirstAsync(
      `SELECT * FROM question_stats WHERE question_id = ?`,
      [questionId]
    ) as QuestionStats | null;
    
    return result;
  } catch (error) {
    console.error('Failed to get question stats:', error);
    throw error;
  }
}

export async function getWrongQuestions(volume?: number): Promise<QuestionStats[]> {
  const db = getDatabase();
  
  try {
    const query = volume
      ? `SELECT * FROM question_stats 
         WHERE volume = ? AND wrong_attempts > 0 
         ORDER BY last_attempted DESC`
      : `SELECT * FROM question_stats 
         WHERE wrong_attempts > 0 
         ORDER BY last_attempted DESC`;
    
    const params = volume ? [volume] : [];
    
    const result = await db.getAllAsync(query, params) as QuestionStats[];
    
    return result;
  } catch (error) {
    console.error('Failed to get wrong questions:', error);
    throw error;
  }
}

export async function getBookmarkedQuestions(volume?: number): Promise<QuestionStats[]> {
  const db = getDatabase();
  
  try {
    const query = volume
      ? `SELECT * FROM question_stats 
         WHERE volume = ? AND is_bookmarked = 1 
         ORDER BY last_attempted DESC`
      : `SELECT * FROM question_stats 
         WHERE is_bookmarked = 1 
         ORDER BY last_attempted DESC`;
    
    const params = volume ? [volume] : [];
    
    const result = await db.getAllAsync(query, params) as QuestionStats[];
    
    return result;
  } catch (error) {
    console.error('Failed to get bookmarked questions:', error);
    throw error;
  }
}

export async function toggleBookmark(questionId: number): Promise<void> {
  const db = getDatabase();
  
  try {
    await db.runAsync(
      `UPDATE question_stats 
       SET is_bookmarked = CASE 
         WHEN is_bookmarked = 1 THEN 0 
         ELSE 1 
       END 
       WHERE question_id = ?`,
      [questionId]
    );
  } catch (error) {
    console.error('Failed to toggle bookmark:', error);
    throw error;
  }
}

export async function updateQuestionNote(
  questionId: number,
  note: string
): Promise<void> {
  const db = getDatabase();
  
  try {
    await db.runAsync(
      `INSERT INTO question_stats (question_id, volume, note)
       VALUES (?, 1, ?)
       ON CONFLICT(question_id) DO UPDATE SET
       note = ?`,
      [questionId, note, note]
    );
  } catch (error) {
    console.error('Failed to update question note:', error);
    throw error;
  }
}