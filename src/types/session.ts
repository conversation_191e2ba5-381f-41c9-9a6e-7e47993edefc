import { ProcessedQuestion } from './question';

export interface PracticeSession {
  id: string;
  title: string;
  questions: ProcessedQuestion[];
  currentIndex: number;
  answers: Record<number, number>;
  startTime: Date;
  endTime?: Date;
  volumes: number[];
  config: PracticeConfig;
}

export interface PracticeConfig {
  volumes: number[]; // Changed from single volume to array of volumes
  chapter: number | null;
  mode: 'sequential' | 'random';
  includeWrongQuestions: boolean;
  includeBookmarked: boolean;
  includeUnseen: boolean; // New option to filter unseen questions
}

export interface SessionStats {
  totalQuestions: number;
  answeredCount: number;
  correctCount: number;
  wrongCount: number;
  accuracy: number;
  timeSpent: number;
}