{"expo": {"name": "macau-drive-exam", "slug": "macau-drive-exam", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "macaudriveexam", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-sqlite", {"enableFTS": true, "useSQLCipher": true, "android": {"enableFTS": true, "useSQLCipher": true}, "ios": {"customBuildFlags": ["-DSQLITE_ENABLE_DBSTAT_VTAB=1 -DSQLITE_ENABLE_SNAPSHOT=1"]}}]], "experiments": {"typedRoutes": true}}}